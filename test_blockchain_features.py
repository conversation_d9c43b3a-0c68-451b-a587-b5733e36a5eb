#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试区块链增强功能的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.message_generator import (
    analyze_message_context, 
    get_personality_traits,
    detect_message_emotion,
    enhanced_post_processing,
    add_natural_elements
)

def test_blockchain_context_analysis():
    """测试区块链相关消息上下文分析"""
    print("=== 测试区块链消息上下文分析 ===")
    
    test_messages = [
        {
            'content': '这个项目的tokenomics怎么样？',
            'mentions_me': False,
            'is_reply_to_me': False,
            'is_reply': False
        },
        {
            'content': '今天BTC pump了不少啊',
            'mentions_me': False,
            'is_reply_to_me': False,
            'is_reply': False
        },
        {
            'content': '有人参加这个空投吗？',
            'mentions_me': False,
            'is_reply_to_me': False,
            'is_reply': False
        },
        {
            'content': 'mainnet什么时候上线？',
            'mentions_me': False,
            'is_reply_to_me': False,
            'is_reply': False
        },
        {
            'content': '这波dip可以加仓了',
            'mentions_me': False,
            'is_reply_to_me': False,
            'is_reply': False
        }
    ]
    
    for msg in test_messages:
        context = analyze_message_context(msg, [], {'username': 'test_user'})
        print(f"消息: '{msg['content']}'")
        print(f"类型: {context['response_type']}")
        print(f"指导: {context['response_guidance'][:50]}...")
        print()

def test_crypto_emotion_detection():
    """测试加密货币相关情感检测"""
    print("=== 测试加密货币情感检测 ===")
    
    crypto_messages = [
        "哇，这个项目要moon了！🚀",
        "又被套了，难受😢",
        "diamond hands，hodl住！💎",
        "这个技术真的很牛逼",
        "FUD太多了，有点烦",
        "bullish af！",
        "paper hands都出局了",
        "这个空投真香",
        "gas费太贵了，生气😡",
        "wagmi！一起建设生态"
    ]
    
    for msg in crypto_messages:
        emotion = detect_message_emotion(msg)
        print(f"'{msg}' -> {emotion if emotion else '无明显情感'}")

def test_crypto_natural_elements():
    """测试加密货币自然化元素"""
    print("\n=== 测试加密货币自然化元素 ===")
    
    test_cases = [
        ("这个项目不错", {'response_type': 'casual'}),
        ("确实很bullish", {'response_type': 'positive'}),
        ("技术细节我不太懂", {'response_type': 'question'}),
        ("DYOR很重要", {'response_type': 'mention'})
    ]
    
    account = {'username': 'crypto_user'}
    
    for reply, context in test_cases:
        print(f"原始: {reply}")
        enhanced_results = []
        for _ in range(3):  # 测试3次看随机性
            enhanced = add_natural_elements(reply, context, account)
            enhanced_results.append(enhanced)
        print(f"增强: {set(enhanced_results)}")
        print()

def test_crypto_post_processing():
    """测试加密货币后处理"""
    print("=== 测试加密货币后处理 ===")
    
    crypto_replies = [
        "这个DeFi项目的APY很高，但是要注意风险",
        "hodl住，diamond hands！💎🙌",
        "mainnet上线后应该会有更多用例",
        "这个NFT的floor price涨了不少",
        "Layer2的gas费确实便宜很多",
        "空投记得及时claim，别错过了"
    ]
    
    account = {'username': 'test_user'}
    selected_message = {'content': '测试消息'}
    message_context = {'response_type': 'casual'}
    
    for reply in crypto_replies:
        processed = enhanced_post_processing(reply, account, selected_message, message_context)
        print(f"原始: {reply}")
        print(f"处理: {processed}")
        print()

def test_crypto_personality():
    """测试加密货币投资者性格特征"""
    print("=== 测试加密货币投资者性格 ===")
    
    crypto_accounts = [
        {'age': 23, 'sex': '男', 'city': '深圳', 'investment_style': 'DeFi farmer'},
        {'age': 26, 'sex': '女', 'city': '上海', 'investment_style': 'NFT collector'},
        {'age': 30, 'sex': '男', 'city': '北京', 'investment_style': 'Long-term hodler'},
        {'age': 25, 'sex': '女', 'city': '杭州', 'investment_style': 'Airdrop hunter'}
    ]
    
    for account in crypto_accounts:
        traits = get_personality_traits(account)
        style = account.get('investment_style', '普通投资者')
        print(f"{account['age']}岁{account['sex']}性，{account['city']}，{style}")
        print(f"性格特征: {traits}")
        print()

def simulate_crypto_conversation():
    """模拟加密货币对话场景"""
    print("=== 模拟加密货币对话场景 ===")
    
    conversation_scenarios = [
        {
            'topic': '价格讨论',
            'messages': [
                'BTC今天涨了5%',
                '这波pump还能持续吗？',
                '我觉得还会继续涨',
                '但是要注意风险控制'
            ]
        },
        {
            'topic': '技术讨论', 
            'messages': [
                '这个Layer2的TPS很高',
                '确实，比主网快多了',
                '但是去中心化程度如何？',
                '技术细节我不太懂'
            ]
        },
        {
            'topic': '空投活动',
            'messages': [
                '新的空投活动开始了',
                '任务复杂吗？',
                '还好，就是交互几次',
                '记得用小号测试'
            ]
        }
    ]
    
    for scenario in conversation_scenarios:
        print(f"话题: {scenario['topic']}")
        for i, msg_content in enumerate(scenario['messages']):
            msg = {
                'content': msg_content,
                'mentions_me': False,
                'is_reply_to_me': False,
                'is_reply': i > 0  # 第一条消息不是回复
            }
            
            context = analyze_message_context(msg, [], {'username': 'test_user'})
            print(f"  消息{i+1}: {msg_content}")
            print(f"  分析: {context['response_type']} - {context['situation_description'][:30]}...")
        print()

if __name__ == "__main__":
    print("Discord区块链机器人增强功能测试")
    print("=" * 60)
    
    try:
        test_blockchain_context_analysis()
        test_crypto_emotion_detection()
        test_crypto_natural_elements()
        test_crypto_post_processing()
        test_crypto_personality()
        simulate_crypto_conversation()
        
        print("=" * 60)
        print("区块链增强功能测试完成！")
        print("\n主要改进:")
        print("✅ 区块链术语识别和使用")
        print("✅ 加密货币情感检测")
        print("✅ 投资者心态模拟")
        print("✅ 技术讨论适度参与")
        print("✅ 价格讨论理性回应")
        print("✅ 空投活动积极参与")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
