import random
import threading
import time
from datetime import datetime, timedelta
from typing import Dict

import yaml

from services.account_manager import account_manager
from services.discord_api import DiscordAPI
from services.logger import logger
from services.errors import ConfigValidationError
from services.message_generator import MessageGenerator
from services.status_manager import StatusManager
from services.tools import get_next_active_time, calculate_delay, \
    validate_config


def load_config(file_path='config.yaml') -> Dict:
    with open(file_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    try:
        return config
    except ConfigValidationError as e:
        logger.error(f'配置项错误: {e}', True)
        exit(1)

def channel_worker(account: Dict, channel: Dict, config: Dict):
    """ 单个频道的消息处理循环 """
    channel_name = channel['name']

    StatusManager.update(account['name'], channel_name, "INFO", "🚀 启动中...")
    consecutive_errors = 0

    daily_limit = channel.get('limit', 0)

    while True:
        tracker = account_manager.get_tracker(account["name"], channel['id'])
        # 检查每日限制
        if 0 < daily_limit <= tracker.get_daily_count():
            now = datetime.now()
            reset_time = datetime(now.year, now.month, now.day) + timedelta(days=1)
            delay = (reset_time - now).total_seconds()
            StatusManager.update(account['name'], channel_name, "INFO",
                                 f"已达每日限制 ({daily_limit}条)",
                                 reset_time.strftime("%Y-%m-%d %H:%M:%S"))
            time.sleep(delay)
            continue

        next_active = get_next_active_time(channel.get("start_time", "08:00:00"), channel.get("end_time", "23:59:59"))
        if next_active:
            sleep_time = (next_active - datetime.now()).total_seconds()
            StatusManager.update(account['name'], channel_name, "INFO", f"⚠️ 不在运行的时间范围内，睡眠 {sleep_time} 秒后启动")
            time.sleep(sleep_time)

        try:
            # 获取最近的消息
            recent_message = DiscordAPI.fetch_message(account, channel)
            if recent_message:
                last_msg = recent_message[-1]
                StatusManager.add_log(account['name'], "INFO", f"[{channel_name}] 频道最新消息 {len(recent_message)} 条")
                if last_msg["username"] == account["username"]:
                    StatusManager.add_log(account['name'], "INFO", f"[{channel_name}] 最近一条消息是自己的，跳过，60秒后再尝试")
                    time.sleep(60)
                    continue
            else:
                StatusManager.add_log(account['name'], "INFO", f"[{channel_name}] 频道最近消息获取失败，90秒后重新尝试")
                time.sleep(90)
                continue

            # 生成消息内容
            content = MessageGenerator.generate(account, channel, config, recent_message)
            if not content:
                StatusManager.add_log(account['name'], "INFO", f"[{channel_name}] 生成消息失败，120秒后重新尝试生成")
                time.sleep(120)
                continue

            # 增强的打字模拟（根据消息长度调整）
            typing_duration = min(max(len(content) / 20, 1.0), 4.0)  # 1-4秒打字时间
            DiscordAPI.typing(account, channel, typing_duration)

            # 发送消息
            if DiscordAPI.send_message(account, channel, content):
                tracker.increment_daily_count()
                consecutive_errors = 0
                delay = calculate_delay(config["min_interval"], config["max_interval"], config["jitter_range"])
                if delay < channel.get("interval", 10):
                    delay = channel.get("interval", 10) + random.randint(5, 20)
                next_time = (datetime.now() + timedelta(seconds=delay)).strftime("%Y-%m-%d %H:%M:%S")

                StatusManager.update(account['name'], channel_name, "SUCCESS", f"消息发送成功：{content}", next_time)
                time.sleep(delay)
            else:
                consecutive_errors += 1
                if consecutive_errors > config["consecutive_errors"]:
                    StatusManager.update(account['name'], channel_name, "ERROR", "消息发送连续失败，停止发送")
                    break
                else:
                    StatusManager.update(account['name'], channel_name, "ERROR", f"消息发送失败，{consecutive_errors} 次", (datetime.now() + timedelta(seconds=90)).strftime("%Y-%m-%d %H:%M:%S"))
                time.sleep(90)
        except Exception as e:
            StatusManager.update(account['name'], channel_name, "ERROR", f"消息发生异常：{e}")
            time.sleep(60)

def account_worker(account: Dict, config: Dict):
    """管理单个账号的所有频道线程"""
    channels = [ch for ch in config["channels"] if ch['name'] in account['enabled_channels']]
    if not channels:
        StatusManager.update(account["name"], "全局", "INFO", "⚠️ 无可用频道")
        return

    threads = []
    for channel in channels:
        if not channel.get("enabled", False):
            StatusManager.update(account['name'], channel['name'], "INFO", f"未启用，请检查配置")
            continue

        thread = threading.Thread(target=channel_worker, args=(account, channel, config), daemon=True)
        threads.append(thread)
        thread.start()
        time.sleep(30)
    for thread in threads:
        thread.join()

def main():
    config = load_config("discord_config/config.yaml")
    config["accounts"] = load_config("discord_config/accounts.yaml")
    config["channels"] = load_config("discord_config/channels.yaml")

    validate_config(config, {'token', 'min_interval', 'max_interval', 'jitter_range', 'channels', 'accounts'})

    # 计算启用的账号数量
    enabled_accounts = [account for account in config['accounts'] if account['enabled']]
    enabled_count = len(enabled_accounts)
    total_count = len(config['accounts'])

    print("\n" + "=" * 100)
    print(f" 启动 Discord 自动聊天系统 - 共 {total_count} 个账号，需要运行的共 {enabled_count} 个账号")
    print("=" * 100 + "\n")

    time.sleep(1)

    for i, account in enumerate(enabled_accounts):
        if not account['enabled_channels'] or not len(account['enabled_channels']):
            StatusManager.update(account['name'], "全局", "INFO", "⚠️ 未启用或未启用频道")
            continue
        threading.Thread(target=account_worker, args=(account,config), daemon=True).start()
        time.sleep(10)

    try:
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        print("\n\n" + "=" * 100)
        print(" 程序已安全停止")
        print("=" * 100)
        logger.info("\n程序已安全停止")

if __name__ == "__main__":
    main()