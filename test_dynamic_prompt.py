#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试动态提示词功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.message_generator import (
    build_dynamic_personality_prompt,
    infer_project_info,
    analyze_message_context
)

def test_dynamic_prompt_generation():
    """测试动态提示词生成"""
    print("=== 测试动态提示词生成 ===")
    
    # 测试账号信息
    test_accounts = [
        {
            'name': 'runtu_small',
            'username': 'runtu_small',
            'age': 25,
            'sex': '男',
            'country': '中国',
            'city': '深圳'
        },
        {
            'name': 'alice_crypto',
            'username': 'alice_crypto',
            'age': 28,
            'sex': '女',
            'country': '美国',
            'city': '纽约'
        }
    ]
    
    # 测试频道信息
    test_channels = [
        {
            'name': 'defi-general',
            'label': 'Uniswap DeFi',
            'desc': 'Uniswap去中心化交易所社区',
            'language': '中文'
        },
        {
            'name': 'nft-collection',
            'label': 'Bored Ape NFT',
            'desc': 'BAYC NFT收藏社区',
            'language': '英语'
        },
        {
            'name': 'gaming-chat',
            'label': 'Axie Infinity',
            'desc': 'Play-to-Earn游戏社区',
            'language': '中文'
        },
        {
            'name': 'layer2-tech',
            'label': 'Arbitrum L2',
            'desc': 'Layer2扩容技术讨论',
            'language': '英语'
        }
    ]
    
    # 测试消息上下文
    test_message = {
        'content': '这个项目的tokenomics怎么样？',
        'mentions_me': False,
        'is_reply_to_me': False,
        'is_reply': False
    }
    
    message_context = analyze_message_context(test_message, [], test_accounts[0])
    
    # 为每个账号和频道组合生成提示词
    for account in test_accounts:
        print(f"\n账号: {account['name']} ({account['age']}岁{account['sex']}性，{account['country']}{account['city']})")
        
        for channel in test_channels:
            print(f"\n频道: {channel['label']} ({channel['name']})")
            print(f"语言: {channel['language']}")
            
            # 生成动态提示词
            prompt = build_dynamic_personality_prompt(account, channel, message_context)
            
            # 只显示前500个字符以便查看
            print("生成的提示词片段:")
            print("-" * 50)
            print(prompt[:500] + "..." if len(prompt) > 500 else prompt)
            print("-" * 50)

def test_project_inference():
    """测试项目类型推断"""
    print("\n=== 测试项目类型推断 ===")
    
    test_cases = [
        ('Uniswap DeFi', 'uniswap-general'),
        ('Bored Ape NFT', 'bayc-collection'),
        ('Axie Infinity', 'axie-gaming'),
        ('Arbitrum L2', 'arbitrum-tech'),
        ('OpenAI Crypto', 'openai-general'),
        ('Dogecoin Meme', 'doge-community'),
        ('Bitcoin Community', 'btc-general'),
        ('Ethereum Discussion', 'eth-chat')
    ]
    
    for label, name in test_cases:
        project_info = infer_project_info(label, name)
        print(f"\n频道: {label} ({name})")
        print(f"推断信息: {project_info}")

def test_multilingual_support():
    """测试多语言支持"""
    print("\n=== 测试多语言支持 ===")
    
    account = {
        'name': 'global_user',
        'username': 'global_user',
        'age': 26,
        'sex': '男',
        'country': '中国',
        'city': '上海'
    }
    
    channels = [
        {
            'name': 'chinese-chat',
            'label': '中文社区',
            'desc': '中文区块链讨论',
            'language': '中文'
        },
        {
            'name': 'english-chat',
            'label': 'English Community',
            'desc': 'English blockchain discussion',
            'language': '英语'
        },
        {
            'name': 'korean-chat',
            'label': '한국 커뮤니티',
            'desc': '한국어 블록체인 토론',
            'language': '韩语'
        }
    ]
    
    test_message = {
        'content': 'gm everyone!',
        'mentions_me': False,
        'is_reply_to_me': False,
        'is_reply': False
    }
    
    message_context = analyze_message_context(test_message, [], account)
    
    for channel in channels:
        print(f"\n语言: {channel['language']}")
        print(f"频道: {channel['label']}")
        
        prompt = build_dynamic_personality_prompt(account, channel, message_context)
        
        # 提取系统信息部分
        lines = prompt.split('\n')
        system_info = []
        for line in lines:
            if line.startswith('- ') or line.startswith('你是'):
                system_info.append(line)
            if len(system_info) >= 5:  # 只显示前几行
                break
        
        print("系统信息:")
        for info in system_info:
            print(f"  {info}")

if __name__ == "__main__":
    print("Discord机器人动态提示词测试")
    print("=" * 60)
    
    try:
        test_dynamic_prompt_generation()
        test_project_inference()
        test_multilingual_support()
        
        print("\n" + "=" * 60)
        print("动态提示词测试完成！")
        print("\n主要功能:")
        print("✅ 动态读取账号个人信息")
        print("✅ 智能推断项目类型和背景")
        print("✅ 支持多语言频道配置")
        print("✅ 根据频道特点调整知识储备")
        print("✅ 灵活的提示词模板系统")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
