import threading
import time

from services.logger import logger
from services.tools import get_time, clear_console

log_lock = threading.Lock()
account_status = {}
display_lock = threading.Lock()
status_lock = threading.Lock()

class StatusManager:
    LOG_MAX_ENTRIES = 50    # 每个账号保留最近50条日志
    LAST_REFRESH = 0
    REFRESH_INTERVAL_SECONDS = 0.5
    LOG_COLORS = {
        'DEBUG': '\033[95m',  # 紫色
        'INFO': '\033[94m',  # 蓝色
        'WARNING': '\033[93m',  # 黄色
        'ERROR': '\033[91m',  # 红色
        'SUCCESS': '\033[92m',  # 绿色
        'RESET': '\033[0m'  # 重置颜色
    }

    @staticmethod
    def add_log(account_name: str, log_type: str, message: str):
        """线程安全的日志记录"""
        if log_type == 'INFO':
            logger.info(message)
        elif log_type == 'DEBUG':
            logger.debug(message)
        elif log_type == 'WARNING':
            logger.warning(message)
        elif log_type == 'ERROR':
            logger.error(message, True)

        with log_lock:
            if account_name not in account_status:
                return

            # 添加带颜色的时间和日志条目
            colored_log = (
                f"{StatusManager.LOG_COLORS.get(log_type, '')}"
                f"[{get_time()}] {message}"
                f"{StatusManager.LOG_COLORS['RESET']}"
            )
            account_status[account_name]['logs'].append(colored_log)
            # 保持日志数量不超过最大值
            if len(account_status[account_name]['logs']) >= StatusManager.LOG_MAX_ENTRIES:
                account_status[account_name]['logs'].pop(0)

    @staticmethod
    def update(account_name: str, channel_name: str, status: str, message: str = "", next_time: str = ""):
        """线程安全的状态更新"""
        now = time.time()
        time_str = get_time()
        with status_lock:
            if account_name not in account_status:
                account_status[account_name] = {'channels': {}, 'logs': [], 'last_update': 0}
            full_status = f"{status} [{time_str}] [{channel_name}] {message[:50]}"
            account_status[account_name]['channels'][channel_name] = {
                'status': full_status,
                'next_time': next_time,
                'timestamp': now,
            }
            account_status[account_name]['last_update'] = now

            StatusManager.add_log(account_name, status, full_status)

            if now - StatusManager.LAST_REFRESH > StatusManager.REFRESH_INTERVAL_SECONDS:
                StatusManager.refresh_display()

    @staticmethod
    def refresh_display():
        if not display_lock.acquire(blocking=False):
            return
        try:
            StatusManager.LAST_REFRESH = time.time()
            clear_console()
            print("\n" + "=" * 80)
            print(f" Discord 自动聊天系统 - 运行中 | 按 Ctrl+C 停止 | 最后刷新：{get_time()}")
            print("=" * 80)

            for acc_name in sorted(account_status.keys()):
                acc_data = account_status[acc_name]
                print(f"\n🔹 账号: {acc_name}")
                print("-" * 80)

                # 显示频道状态
                for ch_name, info in acc_data['channels'].items():
                    status_icon = '🟢' if 'SUCCESS' in info['status'] else '🔴' if 'ERROR' in info['status'] else '🟡'
                    print(f"│ {status_icon} 频道 [{ch_name}]")
                    print(f"│ 状态：{info['status']}")
                    if info['next_time']:
                        print(f"│ 下次发送：{info['next_time']}")
                    print("├" + "-" * 78)

                # 显示日志
                print("\n📜 最近操作记录：")
                for log in acc_data.get('logs', [])[-10:]:  # 显示最后5条日志
                    print(f"  {log}")
            print("\n" + "=" * 80 + "\n")
        finally:
            display_lock.release()