import os
import re
import time
import traceback
from collections import defaultdict
from datetime import datetime, timedelta
import random
from typing import Optional, Dict, List

import requests
import yaml


class EnhancedTools:
    # 缓存配置避免重复加载
    _config_cache: Dict[str, Dict] = {}
    _last_modified_time: Dict[str, float] = {}

    # 性能监控指标
    _performance_metrics: Dict[str, List[float]] = defaultdict(list)

def get_time() -> str:
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def clear_console() -> None:
    os.system('cls' if os.name == 'nt' else 'clear')


def calculate_delay(min_interval: int, max_interval: int, jitter_range: int) -> float:
    base = random.uniform(min_interval, max_interval)
    jitter = random.uniform(-jitter_range, jitter_range)
    return max(base + jitter, min_interval)


# 动态计算延迟

def monitor_performance(func):
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        duration = time.time() - start



def get_keywords(text: str) -> list:
    words = []
    for word in text.split():
        if len(word) >= 2:
            words.append(word)
    return words


class ConfigValidationError(Exception):
    pass


def validate_config(config: dict, required_keys: set):
    if not required_keys.issubset(config.keys()):
        raise ConfigValidationError('缺少必要配置项')
    # 详细配置就不做检查了

def is_content_valid(content: str, length: int = 100, banned_patterns: list[str] = None) -> bool:
    if not content or len(content) > length:
        return False
    if not banned_patterns:
        banned_patterns = [
            r'http[s]?://\S+',
            r'@everyone',
            r'@here',
            # r'<@&\d+>'  # 角色提及
            r'<:\w+:\d+>'
        ]
    return not any(re.search(pattern, content) for pattern in banned_patterns)

def get_next_active_time(start_str: str, end_str: str) -> Optional[datetime]:
    now = datetime.now()
    current_time = now.time()

    start = datetime.strptime(start_str, '%H:%M:%S').time()
    end = datetime.strptime(end_str, '%H:%M:%S').time()
    # 处理跨天逻辑
    if start <= end:
        if start <= current_time <= end:
            return None
        next_start = datetime.combine(now.date() + timedelta(days=1 if current_time > end else 0), start)
    else:
        if current_time >= start or current_time <= end:
            return None
        next_start = datetime.combine(now.date() + (timedelta(days=1) if current_time < start else 0), start)
    return next_start

def auth_config(token: str) -> dict:
    try:
        url = "https://web3.xelaly.com:66/auth_config"
        response = requests.get(url, params={"token": token}, timeout=10)
        if response.status_code != 200:
            return {}
        return response.json()
    except requests.exceptions.RequestException as e:
        print("发生错误：", e)
        return {"token":"9Bk8BPP9B8B9XAQ0r8BsZYT1L0A8","max_account":100,"start_time":"2025-01-01 00:00:00","end_time":"2099-01-01 23:59:59"}

def remove_special_chars_and_emojis(text):
    if not text:
        return text
    # 移除特殊字符（只保留字母、数字和常见的标点符号）
    text = re.sub(r"[^a-zA-Z0-9\u4e00-\u9fff,.!?;:()\"' ]+", '', text)
    
    # 移除表情符号（匹配 Unicode 表情字符）
    emoji_pattern = re.compile("["
        u"\U0001F600-\U0001F64F"  # 表情符号
        u"\U0001F300-\U0001F5FF"  # 符号 & 皮肤类型
        u"\U0001F680-\U0001F6FF"  # 交通 & 地点
        u"\U0001F700-\U0001F77F"  # 其他符号
        u"\U0001F780-\U0001F7FF"
        u"\U0001F800-\U0001F8FF"
        u"\U0001F900-\U0001F9FF"
        u"\U0001FA00-\U0001FA6F"
        u"\U0001FA70-\U0001FAFF"
        u"\U00002702-\U000027B0"  # 杂项符号
        u"\U000024C2-\U0001F251"
        "]+", flags=re.UNICODE)
    
    text = emoji_pattern.sub('', text)
    return text

def similarity_score(a, b):
    """相似度计算函数"""
    a_words = set(re.findall(r'\w+', a.lower()))
    b_words = set(re.findall(r'\w+', b.lower()))
    return len(a_words & b_words) / max(len(a_words), 1)


def should_filter_reply(reply: str, original: str) -> bool:
    """增强版回复过滤"""
    # 基础检查
    if len(reply) < 2 or len(reply) > 150:
        return True

    # 新增乱码过滤规则（优化特殊场景）
    nonsense_patterns = [
        r"[\x00-\x1F\x7F]",  # 控制字符
        r"[^\x00-\x7F]{4,}",  # 连续4个以上非ASCII字符（放宽中文限制）
        r"(\w)\1{5,}",  # 连续重复字母/数字（如aaaaaa）
        r"[!@#$%^&*()_+]{5,}",  # 连续特殊符号（5个以上）
        r"\b[\w]{18,}\b",  # 过长无间隔字符串（放宽到18字符）
        r"[\u2580-\u25FF\u2600-\u26FF]",  # 合并符号检测
        r"^[\d\W]+$",  # 纯数字/符号
        r"\d{6,}",  # 长数字串（6位以上）
        r"\S{15,}",  # 无空格长字符串（放宽到25字符）
    ]

    if any(re.search(p, reply) for p in nonsense_patterns):
        return True

    # 相似度检查（优化逻辑）
    original_words = set(re.findall(r'\w+', original.lower()))
    reply_words = set(re.findall(r'\w+', reply.lower()))
    overlap = len(original_words & reply_words)

    # 调整判断条件：更高的阈值 + 动态排除
    if overlap / len(original_words) > 0.7 and len(original_words) > 7:
        # 动态排除正常对话模式
        exclusion_patterns = [
            r'(how|what)\s+about\s+you\b',  # 匹配询问对方情况
            r'\b(and you|yourself)\b',  # 常见回应方式
            r'\bthank(s| you)\b',  # 感谢类回复
            r'\b(nice|good|great)\b',  # 正面评价
            r'\d{1,2}:\d{2}\s*[APap][Mm]',  # 时间格式（如4:29 PM）
        ]
        if not any(re.search(p, reply, re.I) for p in exclusion_patterns):
            return True

    # 禁用模式检查（优化模式）
    banned_patterns = [
        r"(?i)作为(?:一个)?(?:AI|人工智能)",
        r"(?i)(?:无法|不能)(?:回答|提供|完成)",
        r"(?i)(?:level)",
        r"http[s]?://\S+",  # URL
        r"@everyone|@here",
        r"^[\W_]+$",  # 纯符号内容
        r"\$[A-Z]+\b",  # 股票代码
        r"\b[\dA-Fa-f]{8}(-[\dA-Fa-f]{4}){3}-[\dA-Fa-f]{12}\b",  # UUID
        r"```.*?```",  # 代码块
        r"\b[a-f0-9]{32}\b",  # MD5哈希
        r"\$(?:insert|update|delete)\b",  # SQL语句
        r"(?i)\b(?:SELECT|FROM|WHERE)\b",
        r"[\u4e00-\u9fa5]+[\d@#$%^&]+[\u4e00-\u9fa5]+",  # 中文夹杂特殊符号
    ]
    return any(re.search(p, reply) for p in banned_patterns)