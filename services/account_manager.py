import threading
from typing import Dict

from services.message_tracker import MessageTracker


class AccountManager:
    def __init__(self):
        self.trackers: Dict[str, MessageTracker] = {}
        self.lock = threading.Lock()

    def get_tracker(self, account_name: str, channel_id: str) -> MessageTracker:
        with self.lock:
            key = f"{account_name}-{channel_id}"
            if key not in self.trackers:
                self.trackers[key] = MessageTracker(account_name, channel_id)
            return self.trackers[key]

account_manager = AccountManager()