import random
import re
import time
from datetime import datetime
from typing import Dict, Optional, List

from services.account_manager import account_manager
from services.ai_client import ai_client
from services.consts import PROMPT
from services.message_tracker import MessageTracker
from services.status_manager import StatusManager
from services.tools import remove_special_chars_and_emojis, should_filter_reply


class MessageGenerator:
    @staticmethod
    def get_fixed_message(account: Dict, channel_name: str) -> Optional[str]:
        fixed = account.get('fixed_messages', {}).get(channel_name)
        if fixed and random.random() < 0.5:
            return fixed
        return None

    @classmethod
    def generate(cls, account: Dict, channel: Dict, config: Dict, recent_message: List[Dict]) -> str:
        tracker = account_manager.get_tracker(account["name"], channel['id'])
        tracker.update_mentions(recent_message)

        # 优先使用AI生成
        if channel.get("ai"):
            ai_config = config["ai_configs"][channel["ai"]]
            if reply := generate_ai_reply(account, channel, recent_message, ai_config, tracker):
                return reply
        # if fixed_msg := cls.get_fixed_message(account, channel['name']):
        #     return fixed_msg
        # 从最近得消息中取一条
        # if recent_message and random.random() < 0.3:
        #     return random.choice(recent_message)['content']
        # return random.choice(channel)['messages']
        return ""

def generate_ai_reply(account: Dict, channel: Dict, messages: List[Dict], ai_config: Dict, tracker: MessageTracker) -> Optional[str]:
    """
    使用AI生成回复消息
    1、未处理的@提及优先回复
    2、回复我的消息优先处理
    3、已回复的消息跳过
    4、上下文关联增强
    :param channel: 频道信息
    :param account: 用户的基本信息
    :param messages: 历史聊天消息
    :param ai_config: AI配置
    :param tracker: MessageTracker
    """
    context_messages = []
    try:
        # 消息筛选阶段
        # 步骤1：识别需要处理的消息
        now = time.time()
        pending_messages = []
        active_mentions = tracker.get_active_mentions()

        with tracker.lock:  # 加锁获取最新动态
            last_selected_time = tracker.last_selected_time

        for msg in reversed(messages):  # 逆序处理保证最新消息优先
            # 排除条件优先（原子化判断）
            exclude_conditions = [
                msg["username"] == account["username"],
                tracker.is_replied(msg["id"]),
                now - msg["timestamp"] > 14400,
                msg["timestamp"] < last_selected_time,
            ]
            if any(exclude_conditions):
                continue

            # 计算优先级
            priority = 0
            # 优先级顺序：回复我的 > @提及我的 > 普通消息
            if msg['is_reply_to_me']:
                priority += 40
                if msg['mentions_me']:
                    priority += 15  # 同时@提及得情况
            elif msg["id"] in active_mentions:
                priority += 35  # 未处理的@提及
            elif msg["mentions_me"]:
                priority += 30  # 新@提及
            else:
                # 优化：初始15分，每分钟衰减1分，最低5分（提升普通消息权重）
                time_diff = now - msg["timestamp"]
                priority = max(10, 25 - int(time_diff / 45))    # 每45秒衰减1分

                # 附加权重：如果消息包含疑问词（简单版）
                if any(q in msg["content"] for q in ["?", "？", "吗", "如何", "怎么", "什么", "为什么", "呢"]):
                    priority += 8
                # if len(msg["content"]) > 15:    # 长内容更有回复价值
                #     priority += 5

            # 对话关联性加分
            if msg.get('referenced_message'):
                if msg['referenced_message']['author']['username'] == account['username']:
                    priority += 10       # 关联到我的对话线程
                else:
                    # 检查是否在对话线索中
                    parent_msg = next((m for m in messages if m["id"] == msg["referenced_message"]["id"]), None)
                    if parent_msg and parent_msg["username"] == account["username"]:
                        priority += 8
            pending_messages.append((priority, msg))

        if not pending_messages:
            StatusManager.add_log(account['name'], 'INFO', f"[{channel['name']}] 无符合条件的新消息")
            return None

        # 选择策略优化：当最高优先级<20时，优先选择最新消息
        max_priority = max(p[0] for p in pending_messages)

        active_level = 0
        # 选择逻辑
        if max_priority > 30: # 高优先级消息
            # 选择最紧急的消息（优先级最高 + 时间最近）
            selected = max(pending_messages, key=lambda x: (x[0], x[1]['timestamp']))
        else:
            # 普通消息选择最新3条中的最优
            recent_messages = [m for m in pending_messages if now - m[1]['timestamp'] < 600]
            if recent_messages:
                active_level = len(recent_messages) / 20
                selected = max(recent_messages, key=lambda x: (x[0], x[1]['timestamp']))
            else:
                selected = max(pending_messages, key=lambda x: x[0])

        selected_message = selected[1]
        StatusManager.add_log(account['name'], 'DEBUG', f"[{channel['name']}] 选择消息：{selected_message['name']} - {selected_message['id']} - {selected_message['content']}")

        # 更新最后选中消息的时间戳
        with tracker.lock:
            tracker.last_selected_time = max(tracker.last_selected_time, selected_message['timestamp'])

        # 分析消息类型和上下文
        message_context = analyze_message_context(selected_message, messages, account)

        personality_prompt = f"""
        # 系统信息
        - 当前时间：{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} UTC+8
        - 频道：{channel['label']}
        - 语言：{channel.get('language', '中文')}

        # 身份设定
        你是{account['name']}（@{account['username']}），{account.get('age', 25)}岁{account.get('sex', '男')}性，来自{account.get('country', '中国')}{account.get('city', '深圳')}。
        你是{channel['label']}Discord群组的活跃成员，性格{get_personality_traits(account)}。

        # 当前对话情况
        {message_context['situation_description']}

        # 回复指导
        {message_context['response_guidance']}

        """ + PROMPT

        context_messages = [{"role": "system", "content": personality_prompt}]

        # 收集对话线程（最多10条）
        thread_messages = []
        current_msg = selected_message
        # 向上追溯对话线索
        for _ in range(10):
            if current_msg.get('referenced_message'):
                parent = next((m for m in messages if m['id'] == current_msg['referenced_message']['id']), None)
                if parent and parent not in thread_messages:
                    thread_messages.insert(0, parent)
                    current_msg = parent
                else:
                    break

        # 添加当前对话线程
        thread_messages.append(selected_message)

        # 向下收集相关回复
        current_index = messages.index(selected_message)
        for msg in messages[current_index + 1: current_index + 3]:
            if msg.get('referenced_message') and msg['referenced_message']['id'] == selected_message['id']:
                thread_messages.append(msg)

        # 智能上下文选择
        context_messages_data = build_smart_context(messages, selected_message, account, current_index)
        for msg in context_messages_data:
            if msg not in thread_messages:
                thread_messages.append(msg)

        # 去重并排序
        thread_messages = sorted(
            {m['id']: m for m in thread_messages}.values(),
            key=lambda x: x['timestamp']
        )[-12:]  # 保持最近的12条

        # 构建增强的上下文消息
        for msg in thread_messages[-12:]:
            role = "assistant" if msg['username'] == account["username"] else "user"

            # 增强的消息标记
            prefix = []
            if msg.get('mentions_me'):
                prefix.append("[@你]")
            if msg.get('is_reply_to_me'):
                prefix.append("[回复你]")
            if msg.get('is_reply') and not msg.get('is_reply_to_me'):
                prefix.append("[回复他人]")

            # 添加情感和语气标记
            emotion_tag = detect_message_emotion(msg['content'])
            if emotion_tag:
                prefix.append(f"[{emotion_tag}]")

            # 时间相关性标记
            time_diff = now - msg['timestamp']
            if time_diff < 300:  # 5分钟内
                prefix.append("[刚刚]")
            elif time_diff < 1800:  # 30分钟内
                prefix.append("[最近]")

            content_with_context = f"{' '.join(prefix)} {msg['content']}" if prefix else msg['content']

            context_messages.append({
                "role": role,
                "content": content_with_context,
                "name": remove_special_chars_and_emojis(msg.get('name', msg['username']))
            })

        StatusManager.add_log(account['name'], "DEBUG", f"[{channel['name']}] 上下文消息数：{len(context_messages)}")

        # ================== AI生成阶段 ==================
        client = ai_client.get_client(ai_config)

        # 动态调整生成参数
        temperature = calculate_dynamic_temperature(selected_message, active_level, account)
        max_tokens = calculate_max_tokens(message_context['response_type'])

        response = client.chat.completions.create(
            messages=context_messages,
            model=ai_config['model'],
            max_tokens=max_tokens,
            temperature=temperature,
            response_format={"type": "text"},
            presence_penalty=0.1,  # 减少重复
            frequency_penalty=0.2,  # 鼓励多样性
        )

        raw_reply = response.choices[0].message.content.strip()

        # ================== 增强后处理阶段 ==================
        clean_reply = enhanced_post_processing(raw_reply, account, selected_message, message_context)

        # 增强的过滤和检测
        if enhanced_reply_filter(clean_reply, selected_message, context_messages, account):
            StatusManager.add_log(account['name'], "WARNING", f"频道 [{channel['name']}] 过滤无效回复：{clean_reply}")
            return None

        # 智能重复检测
        if smart_duplicate_detection(clean_reply, tracker, selected_message):
            StatusManager.add_log(account['name'], "WARNING", f"频道 [{channel['name']}] 检测到重复回复：{clean_reply}")
            return None

        # 增强的消息跟踪
        enhanced_message_tracking(tracker, selected_message, clean_reply, message_context)

        StatusManager.add_log(account['name'], "INFO", f"频道 [{channel['name']}] AI生成回复：{clean_reply} | 类型：{message_context['response_type']}")
        return clean_reply
    except Exception as e:
        StatusManager.add_log(account['name'], "ERROR", f"频道 [{channel['name']}] AI生成失败：{e} | 上下文：{len(context_messages) if 'context_messages' in locals() else 0}条消息")
        return None


# ================== 增强功能函数 ==================

def analyze_message_context(selected_message: Dict, messages: List[Dict], account: Dict) -> Dict:
    """分析消息上下文，确定回复策略"""
    context = {
        'response_type': 'normal',
        'situation_description': '',
        'response_guidance': ''
    }

    # 检测消息类型
    if selected_message.get('mentions_me'):
        context['response_type'] = 'mention'
        context['situation_description'] = f"有人@提及了你，消息内容：{selected_message['content']}"
        context['response_guidance'] = "友好回应@提及，表现出被叫到的自然反应。可以询问对方需要什么帮助或表达关注。"

    elif selected_message.get('is_reply_to_me'):
        context['response_type'] = 'reply_to_me'
        context['situation_description'] = f"有人回复了你的消息，内容：{selected_message['content']}"
        context['response_guidance'] = "继续对话，保持话题连贯性。可以进一步讨论、表达观点或询问更多信息。"

    elif selected_message.get('is_reply'):
        context['response_type'] = 'reply_to_others'
        context['situation_description'] = f"这是对他人消息的回复：{selected_message['content']}"
        context['response_guidance'] = "可以加入讨论，提供不同观点或相关经验分享。保持友好和建设性。"

    else:
        # 分析消息内容特征
        content = selected_message['content'].lower()

        if any(q in content for q in ['?', '？', '吗', '如何', '怎么', '什么', '为什么', '呢']):
            context['response_type'] = 'question'
            context['situation_description'] = f"有人提出了问题：{selected_message['content']}"
            context['response_guidance'] = "尝试回答问题或分享相关经验。如果不确定答案，可以诚实表达不知道或寻求他人帮助。"

        elif any(emotion in content for emotion in ['哈哈', '笑', '开心', '高兴', '激动']):
            context['response_type'] = 'positive'
            context['situation_description'] = f"有人表达了积极情绪：{selected_message['content']}"
            context['response_guidance'] = "回应积极情绪，可以表达共鸣、祝贺或分享类似的快乐体验。"

        elif any(emotion in content for emotion in ['难过', '失望', '郁闷', '烦', '累']):
            context['response_type'] = 'negative'
            context['situation_description'] = f"有人表达了消极情绪：{selected_message['content']}"
            context['response_guidance'] = "表达理解和支持，可以安慰对方或分享鼓励的话语。避免过于深入的建议。"

        else:
            context['response_type'] = 'casual'
            context['situation_description'] = f"日常聊天消息：{selected_message['content']}"
            context['response_guidance'] = "自然地参与对话，可以表达观点、分享经验或提出相关话题。保持轻松友好的语调。"

    return context


def get_personality_traits(account: Dict) -> str:
    """根据账号信息生成性格特征描述"""
    age = account.get('age', 25)
    sex = account.get('sex', '男')
    city = account.get('city', '深圳')

    traits = []

    if age < 25:
        traits.append('年轻活泼')
    elif age < 30:
        traits.append('成熟稳重')
    else:
        traits.append('经验丰富')

    if sex == '男':
        traits.extend(['直率', '幽默'])
    else:
        traits.extend(['细心', '温和'])

    if city in ['北京', '上海', '深圳', '广州']:
        traits.append('见识广泛')
    else:
        traits.append('朴实真诚')

    return '、'.join(traits)


def detect_message_emotion(content: str) -> Optional[str]:
    """检测消息情感"""
    content_lower = content.lower()

    if any(word in content_lower for word in ['哈哈', '笑', '😂', '😄', '开心', '高兴']):
        return '开心'
    elif any(word in content_lower for word in ['难过', '😢', '😭', '失望', '郁闷']):
        return '难过'
    elif any(word in content_lower for word in ['生气', '😡', '愤怒', '烦']):
        return '生气'
    elif any(word in content_lower for word in ['惊讶', '😮', '哇', '天哪']):
        return '惊讶'
    elif any(word in content_lower for word in ['?', '？', '疑问', '不懂']):
        return '疑问'

    return None


def build_smart_context(messages: List[Dict], selected_message: Dict, account: Dict, current_index: int) -> List[Dict]:
    """智能构建上下文消息"""
    context_messages = []

    # 1. 添加回复链条
    if selected_message.get('is_reply') and selected_message.get('referenced_message'):
        ref_msg = selected_message['referenced_message']
        context_messages.append({
            'id': ref_msg['id'],
            'username': ref_msg['author']['username'],
            'name': ref_msg['author'].get('global_name', ref_msg['author']['username']),
            'content': ref_msg['content'],
            'timestamp': time.time() - 300,  # 假设是较早的消息
            'is_reference': True
        })

    # 2. 添加相关对话
    start_idx = max(0, current_index - 6)
    end_idx = min(len(messages), current_index + 3)

    for i in range(start_idx, end_idx):
        msg = messages[i]
        if msg['id'] != selected_message['id']:
            context_messages.append(msg)

    # 3. 去重并排序
    unique_messages = {}
    for msg in context_messages:
        if msg['id'] not in unique_messages:
            unique_messages[msg['id']] = msg

    return sorted(unique_messages.values(), key=lambda x: x['timestamp'])


def calculate_dynamic_temperature(selected_message: Dict, active_level: float, account: Dict) -> float:
    """动态计算生成温度"""
    base_temp = 0.7

    # 根据消息类型调整
    if selected_message.get('mentions_me'):
        base_temp += 0.1  # @提及时更活跃
    elif selected_message.get('is_reply_to_me'):
        base_temp += 0.05  # 回复时稍微活跃

    # 根据活跃度调整
    base_temp += active_level * 0.2

    # 根据时间调整（晚上更随意）
    hour = datetime.now().hour
    if 22 <= hour or hour <= 6:
        base_temp += 0.1

    return min(max(base_temp, 0.3), 1.0)


def calculate_max_tokens(response_type: str) -> int:
    """根据回复类型计算最大token数"""
    token_map = {
        'mention': 100,      # @提及回复较短
        'reply_to_me': 120,  # 回复我的消息可以稍长
        'question': 150,     # 回答问题可能需要更多内容
        'positive': 80,      # 积极回应通常较短
        'negative': 100,     # 安慰消息适中
        'casual': 90,        # 日常聊天较短
        'normal': 100        # 默认长度
    }

    return token_map.get(response_type, 100)


def enhanced_post_processing(raw_reply: str, account: Dict, selected_message: Dict, message_context: Dict) -> str:
    """增强的后处理"""
    clean_reply = raw_reply.strip()

    # 1. 移除多余标记和格式
    clean_reply = re.sub(rf"@{re.escape(account['username'])}\s*", "", clean_reply)
    clean_reply = clean_reply.replace("**", "").replace("`", "")
    clean_reply = re.sub(r'^["\']|["\']$', '', clean_reply)  # 移除首尾引号

    # 2. 标点符号标准化
    clean_reply = clean_reply.translate(str.maketrans({
        "＠": "@", "！": "!", "？": "?", "，": ",", "。": ".",
        "：": ":", "；": ";", "“": '"', "”": '"', "‘": "'", "’": "'"
    }))

    # 3. 空格处理
    clean_reply = re.sub(r'\s{2,}', ' ', clean_reply)
    clean_reply = re.sub(r'^[\s\u3000\-*_]+|[\s\u3000\-*_]+$', '', clean_reply)

    # 4. 长度控制和智能截断
    max_length = 120
    if len(clean_reply) > max_length:
        # 寻找合适的截断点
        truncate_points = ["。", "！", "？", ".", "!", "?"]
        best_cut = -1

        for point in truncate_points:
            pos = clean_reply.rfind(point, 0, max_length)
            if pos > max_length * 0.7:  # 至少保留70%的内容
                best_cut = pos + 1
                break

        if best_cut > 0:
            clean_reply = clean_reply[:best_cut]
        else:
            clean_reply = clean_reply[:max_length-3] + "..."

    # 5. 添加自然化元素
    clean_reply = add_natural_elements(clean_reply, message_context, account)

    return clean_reply


def add_natural_elements(reply: str, message_context: Dict, account: Dict) -> str:
    """添加自然化元素"""
    # 3%概率添加打字错误
    if random.random() < 0.03:
        reply = add_typo(reply)

    # 根据消息类型添加语气词
    if message_context['response_type'] == 'mention':
        if random.random() < 0.3:
            reply = random.choice(["嗯？", "诶？", "咕了？"]) + " " + reply

    elif message_context['response_type'] == 'positive':
        if random.random() < 0.2:
            reply += random.choice([" 哈哈", " 😄", " 👍"])

    return reply


def add_typo(text: str) -> str:
    """添加轻微的打字错误"""
    if len(text) < 5:
        return text

    typo_map = {
        '的': '得', '得': '的', '在': '再', '再': '在',
        '你': '尼', '我': '窝', '是': '事', '这': '着'
    }

    for original, typo in typo_map.items():
        if original in text and random.random() < 0.5:
            text = text.replace(original, typo, 1)
            break

    return text


def enhanced_reply_filter(reply: str, selected_message: Dict, context_messages: List[Dict], account: Dict) -> bool:
    """增强的回复过滤"""
    # 使用原有的过滤逻辑
    if should_filter_reply(reply, selected_message['content']):
        return True

    # 新增过滤规则

    # 1. 检查是否过于相似于最近的消息
    recent_contents = [msg['content'] for msg in context_messages[-3:] if msg.get('role') == 'assistant']
    for recent in recent_contents:
        if calculate_similarity(reply, recent) > 0.8:
            return True

    # 2. 检查是否包含不当内容
    inappropriate_patterns = [
        r'作为.*AI', r'我是.*助手', r'我不能.*', r'我无法.*',
        r'根据.*信息', r'基于.*数据', r'算法.*', r'模型.*'
    ]

    for pattern in inappropriate_patterns:
        if re.search(pattern, reply, re.IGNORECASE):
            return True

    # 3. 检查长度合理性
    if len(reply) < 2 or len(reply) > 150:
        return True

    return False


def calculate_similarity(text1: str, text2: str) -> float:
    """计算文本相似度"""
    words1 = set(re.findall(r'\w+', text1.lower()))
    words2 = set(re.findall(r'\w+', text2.lower()))

    if not words1 or not words2:
        return 0.0

    intersection = len(words1 & words2)
    union = len(words1 | words2)

    return intersection / union if union > 0 else 0.0


def smart_duplicate_detection(reply: str, tracker: MessageTracker, selected_message: Dict) -> bool:
    """智能重复检测"""
    # 使用原有的重复检测
    if tracker.is_reply_duplicate(reply):
        return True

    # 新增智能检测

    # 1. 检查是否与最近回复过于相似
    with tracker.lock:
        recent_replies = list(tracker.sent_history.keys())[-5:]  # 最近5条回复

        for recent in recent_replies:
            if calculate_similarity(reply, recent) > 0.7:
                return True

    # 2. 检查是否是简单重复的模式
    simple_patterns = [r'^(好的|嗯|哦|是的|对的)$', r'^(哈哈|呵呵|嘉嘉)+$']

    for pattern in simple_patterns:
        if re.match(pattern, reply.strip()):
            # 简单回复需要更严格的重复检测
            if reply.strip() in recent_replies:
                return True

    return False


def enhanced_message_tracking(tracker: MessageTracker, selected_message: Dict, reply: str, message_context: Dict):
    """增强的消息跟踪"""
    # 原有的跟踪逻辑
    tracker.add_replied(selected_message['id'])
    tracker.record_sent_reply(reply)

    # 处理@提及
    if selected_message.get('mentions_me'):
        with tracker.lock:
            if selected_message['id'] in tracker.mentioned_history:
                tracker.del_mentioned(selected_message['id'])

    # 新增跟踪信息

    # 1. 记录回复类型统计
    response_type = message_context['response_type']
    if not hasattr(tracker, 'response_type_stats'):
        tracker.response_type_stats = {}

    tracker.response_type_stats[response_type] = tracker.response_type_stats.get(response_type, 0) + 1

    # 2. 更新最后回复时间
    tracker.last_reply_time = time.time()

    # 3. 如果是回复链条，标记相关消息
    if selected_message.get('is_reply') and selected_message.get('referenced_message'):
        ref_id = selected_message['referenced_message']['id']
        tracker.add_replied(ref_id)  # 也标记被回复的消息