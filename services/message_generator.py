import random
import re
import time
from datetime import datetime
from typing import Dict, Optional, List

from services.account_manager import account_manager
from services.ai_client import ai_client
from services.consts import PROMPT
from services.message_tracker import MessageTracker
from services.status_manager import StatusManager
from services.tools import remove_special_chars_and_emojis, should_filter_reply


class MessageGenerator:
    @staticmethod
    def get_fixed_message(account: Dict, channel_name: str) -> Optional[str]:
        fixed = account.get('fixed_messages', {}).get(channel_name)
        if fixed and random.random() < 0.5:
            return fixed
        return None

    @classmethod
    def generate(cls, account: Dict, channel: Dict, config: Dict, recent_message: List[Dict]) -> str:
        tracker = account_manager.get_tracker(account["name"], channel['id'])
        tracker.update_mentions(recent_message)

        # 优先使用AI生成
        if channel.get("ai"):
            ai_config = config["ai_configs"][channel["ai"]]
            if reply := generate_ai_reply(account, channel, recent_message, ai_config, tracker):
                return reply
        # if fixed_msg := cls.get_fixed_message(account, channel['name']):
        #     return fixed_msg
        # 从最近得消息中取一条
        # if recent_message and random.random() < 0.3:
        #     return random.choice(recent_message)['content']
        # return random.choice(channel)['messages']
        return ""

def generate_ai_reply(account: Dict, channel: Dict, messages: List[Dict], ai_config: Dict, tracker: MessageTracker) -> Optional[str]:
    """
    使用AI生成回复消息
    1、未处理的@提及优先回复
    2、回复我的消息优先处理
    3、已回复的消息跳过
    4、上下文关联增强
    :param channel: 频道信息
    :param account: 用户的基本信息
    :param messages: 历史聊天消息
    :param ai_config: AI配置
    :param tracker: MessageTracker
    """
    context_messages = []
    try:
        # 消息筛选阶段
        # 步骤1：识别需要处理的消息
        now = time.time()
        pending_messages = []
        active_mentions = tracker.get_active_mentions()

        with tracker.lock:  # 加锁获取最新动态
            last_selected_time = tracker.last_selected_time

        for msg in reversed(messages):  # 逆序处理保证最新消息优先
            # 排除条件优先（原子化判断）
            exclude_conditions = [
                msg["username"] == account["username"],
                tracker.is_replied(msg["id"]),
                now - msg["timestamp"] > 14400,
                msg["timestamp"] < last_selected_time,
            ]
            if any(exclude_conditions):
                continue

            # 计算优先级
            priority = 0
            # 优先级顺序：回复我的 > @提及我的 > 普通消息
            if msg['is_reply_to_me']:
                priority += 40
                if msg['mentions_me']:
                    priority += 15  # 同时@提及得情况
            elif msg["id"] in active_mentions:
                priority += 35  # 未处理的@提及
            elif msg["mentions_me"]:
                priority += 30  # 新@提及
            else:
                # 优化：初始15分，每分钟衰减1分，最低5分（提升普通消息权重）
                time_diff = now - msg["timestamp"]
                priority = max(10, 25 - int(time_diff / 45))    # 每45秒衰减1分

                # 附加权重：如果消息包含疑问词（简单版）
                if any(q in msg["content"] for q in ["?", "？", "吗", "如何", "怎么", "什么", "为什么", "呢"]):
                    priority += 8
                # if len(msg["content"]) > 15:    # 长内容更有回复价值
                #     priority += 5

            # 对话关联性加分
            if msg.get('referenced_message'):
                if msg['referenced_message']['author']['username'] == account['username']:
                    priority += 10       # 关联到我的对话线程
                else:
                    # 检查是否在对话线索中
                    parent_msg = next((m for m in messages if m["id"] == msg["referenced_message"]["id"]), None)
                    if parent_msg and parent_msg["username"] == account["username"]:
                        priority += 8
            pending_messages.append((priority, msg))

        if not pending_messages:
            StatusManager.add_log(account['name'], 'INFO', f"[{channel['name']}] 无符合条件的新消息")
            return None

        # 选择策略优化：当最高优先级<20时，优先选择最新消息
        max_priority = max(p[0] for p in pending_messages)

        active_level = 0
        # 选择逻辑
        if max_priority > 30: # 高优先级消息
            # 选择最紧急的消息（优先级最高 + 时间最近）
            selected = max(pending_messages, key=lambda x: (x[0], x[1]['timestamp']))
        else:
            # 普通消息选择最新3条中的最优
            recent_messages = [m for m in pending_messages if now - m[1]['timestamp'] < 600]
            if recent_messages:
                active_level = len(recent_messages) / 20
                selected = max(recent_messages, key=lambda x: (x[0], x[1]['timestamp']))
            else:
                selected = max(pending_messages, key=lambda x: x[0])

        selected_message = selected[1]
        StatusManager.add_log(account['name'], 'DEBUG', f"[{channel['name']}] 选择消息：{selected_message['name']} - {selected_message['id']} - {selected_message['content']}")

        # 更新最后选中消息的时间戳
        with tracker.lock:
            tracker.last_selected_time = max(tracker.last_selected_time, selected_message['timestamp'])

        personality_prompt = f"""
        # 系统信息
        - 当前时间：{datetime.now().strftime("%Y-%m-%d :%H:%M:%S")} UTC+8
        # 核心身份认知
        你是{account['name']}（@{account['username']}），是{channel['label']}的discord群组中的一员。在Discord社群中的角色定位：
        - 定位：热情的社区活跃成员（非官方人员）
        - 知识范围：熟悉基础操作，但对技术细节不了解
        - 性格特征：健谈但谨慎、友好且带点幽默感
        - 基础属性：来自{account.get('country', '中国')}{account.get('city', '深圳')}，{account.get('sex', '男')}性，{account.get('age', 25)}岁
""" + PROMPT

        context_messages = [{"role": "system", "content": personality_prompt}]

        # 收集对话线程（最多10条）
        thread_messages = []
        current_msg = selected_message
        # 向上追溯对话线索
        for _ in range(10):
            if current_msg.get('referenced_message'):
                parent = next((m for m in messages if m['id'] == current_msg['referenced_message']['id']), None)
                if parent and parent not in thread_messages:
                    thread_messages.insert(0, parent)
                    current_msg = parent
                else:
                    break

        # 添加当前对话线程
        thread_messages.append(selected_message)

        # 向下收集相关回复
        current_index = messages.index(selected_message)
        for msg in messages[current_index + 1: current_index + 3]:
            if msg.get('referenced_message') and msg['referenced_message']['id'] == selected_message['id']:
                thread_messages.append(msg)

        # 添加邻近上下文（前后各3条）
        start_idx = max(0, current_index - 5)
        end_idx = min(len(messages), current_index + 4)
        for msg in messages[start_idx:end_idx]:
            if msg not in thread_messages:
                thread_messages.append(msg)

        # 去重并排序
        thread_messages = sorted(
            {m['id']: m for m in thread_messages}.values(),
            key=lambda x: x['timestamp']
        )[-12:]  # 保持最近的12条

        # 构建上下文消息
        for msg in thread_messages[-10:]:
            role = "assistant" if msg['username'] == account["username"] else "user"
            prefix = []
            if msg.get('mentions_me'):
                prefix.append("[@你]")
            if msg.get('is_reply_to_me'):
                prefix.append("[回复你]")

            context_messages.append({
                "role": role,
                "content": f"{' '.join(prefix)} {msg['content']}" if prefix else msg['content'],
                "name": remove_special_chars_and_emojis(msg['name'])  # 有助于模型理解对话角色
            })

        StatusManager.add_log(account['name'], "DEBUG", f"[{channel['name']}] 上下文消息数：{len(context_messages)}")

        # ================== AI生成阶段 ==================
        client = ai_client.get_client(ai_config)
        response = client.chat.completions.create(
            messages=context_messages,
            model=ai_config['model'],
            max_tokens=150,
            temperature=0.5 + (0.3 * active_level),
            response_format={"type": "text"},
        )

        raw_reply = response.choices[0].message.content.strip()

        # ================== 后处理阶段 ==================
        # 1. 移除多余标记
        clean_reply = re.sub(rf"@{re.escape(account['username'])}\s*", "", raw_reply)
        clean_reply = clean_reply.replace("**", "").replace("`", "")
        # 2. 替换全角符号
        clean_reply = clean_reply.translate(str.maketrans({
            "＠": "@", "！": "!", "？": "?", "，": ",", "。": ".",
            "：": ":", "；": ";", "“": '"', "”": '"', "‘": "'", "’": "'"
        }))
        # 3. 移除首尾无效符号
        clean_reply = re.sub(r'^[\s\u3000\-*_]+|[\s\u3000\-*_]+$', '', clean_reply)

        # 4. 合并多余空格
        clean_reply = re.sub(r'\s{2,}', ' ', clean_reply)

        # 3. 智能截断
        if len(clean_reply) > 120:
            last_punct = max(clean_reply.rfind("。"), clean_reply.rfind("！"),
                             clean_reply.rfind("？"), clean_reply.rfind("."))
            clean_reply = clean_reply[:last_punct + 1] if last_punct != -1 else clean_reply[:117] + "..."

        if should_filter_reply(clean_reply, selected_message['content']):
            StatusManager.add_log(account['name'], "WARNING", f"频道 [{channel['name']}] 过滤无效回复：{clean_reply}")
            return None

        # 检测内容重复
        if tracker.is_reply_duplicate(clean_reply):
            StatusManager.add_log(account['name'], "WARNING", f"频道 [{channel['name']}] 检测到重复回复：{clean_reply}")
            return None

        # 标记处理的消息（包括相关消息）
        tracker.add_replied(selected_message['id'])
        tracker.record_sent_reply(clean_reply)
        # 如果是提及消息，更新处理状态
        if selected_message.get('mentions_me'):
            with tracker.lock:
                if selected_message['id'] in tracker.mentioned_history:
                    tracker.del_mentioned(selected_message['id'])

        StatusManager.add_log(account['name'], "INFO", f"频道 [{channel['name']}] AI生成回复：{clean_reply}")
        return clean_reply
    except Exception as e:
        StatusManager.add_log(account['name'], "ERROR", f"频道 [{channel['name']}] AI生成失败：{e} | 上下文：{context_messages[-2:]}")
        return None