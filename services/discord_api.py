import time
from datetime import datetime
from typing import Dict, List

import requests

from services.status_manager import StatusManager
from services.tools import is_content_valid


class DiscordAPI:
    @staticmethod
    def build_headers(account: Dict) -> Dict:
        return {
            "Authorization": f"{account['token']}",
            "Content-Type": "application/json",
            "X-Super-Properties": account['super_properties'],
            "Cookie": account['cookie'],
            "Sec-Ch-Ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            "Sec-Ch-Ua_mobile": "?0",
            "Sec-Ch-Ua_Platform": '"Windows"',
            "User-Agent": account.get("user_agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36"),
            "X-Debug-Options": "bugReporterEnabled",
            "X-Discord-Locale": "zh-CN",
            "X-Discord-Timezone": account.get("timezone", "Asia/Shanghai"),
            "Origin": "https://discord.com",
            "Priority": "u=1,i",
        }

    @staticmethod
    def get_proxies(account: Dict) -> Dict:
        return {"http": account['proxy'], "https": account['proxy']} if account.get('proxy') else None

    @classmethod
    def typing(cls, account: Dict, channel: Dict) -> bool:
        url = f"https://discord.com/api/v9/channels/{channel['id']}/typing"
        try:
            response = requests.post(url, headers=cls.build_headers(account), proxies=cls.get_proxies(account), timeout=10)
            return response.status_code == 204
        except Exception as e:
            StatusManager.add_log(account['name'], "ERROR", f"[{channel['name']}] 发送输入中状态失败，错误：{e}")
            return False

    @classmethod
    def send_message(cls, account: Dict, channel: Dict, content: str, referenced: Dict = None) -> bool:
        url = f"https://discord.com/api/v9/channels/{channel['id']}/messages"
        payload = {
            'content': content,
            'flags': 0,
            'mobile_network_type': 'unknown',
            'tts': False,
        }
        retry_count = 0
        max_retries = 5
        base_delay = 1.0

        while retry_count < max_retries:
            try:
                response = requests.post(url, json=payload, headers=cls.build_headers(account), proxies=cls.get_proxies(account), timeout=10)
                if response.status_code == 200:
                    return True
                elif response.status_code == 429:
                    retry_after = response.json().get('retry_after', 60)
                    if response.json().get('message', '') == '您在频道上进行的写入操作已达写入频率限制。':
                        retry_after = 120
                    StatusManager.add_log(account['name'], "INFO", f"[{channel['name']}] 触发速率限制，等待 {retry_after} 秒")
                    time.sleep(retry_after + 3)

                    retry_after = response.json().get('retry_after', 60)
                    StatusManager.add_log(account['name'], "INFO", f"[{channel['name']}] 触发速率限制，等待 {retry_after} 秒")
                    time.sleep(retry_after + 3)
                else:
                    StatusManager.add_log(account['name'], "WARNING", f"[{channel['name']}] 发送失败，状态码：{response.status_code} 响应：{response.text}")
                    time.sleep(base_delay * (2 ** retry_count))
                    retry_count += 1
            except Exception as e:
                StatusManager.add_log(account['name'], "ERROR", f"[{channel['name']}] 发送失败，错误：{e}")
                time.sleep(base_delay * (2 ** retry_count))
                retry_count += 1
        StatusManager.update(account['name'], channel['name'], "ERROR", f"[{channel['name']}] 发送失败，已达最大重试次数 {max_retries}")
        return False

    @classmethod
    def fetch_message(cls, account: Dict, channel: Dict, limit: int = 20) -> List[Dict]:
        url = f"https://discord.com/api/v9/channels/{channel['id']}/messages"
        try:
            StatusManager.add_log(account['name'], "INFO", f"[{channel['name']}] 正在获取最新消息")
            response = requests.get(url, headers=cls.build_headers(account), timeout=10, proxies=cls.get_proxies(account), params={'limit': limit})
            if response.status_code == 200:
                messages = response.json()
                filtered_messages = []
                for msg in messages:
                    processed = {
                        "id": msg['id'],
                        "type": msg['type'],
                        "user_id": msg["author"]["id"],
                        "username": msg["author"]["username"],
                        "name": msg["author"]["global_name"],
                        "content": msg['content'],
                        "timestamp": datetime.strptime(msg['timestamp'], "%Y-%m-%dT%H:%M:%S.%f%z").timestamp(),
                        "mentions": [m["username"] for m in msg['mentions']],
                        "is_reply": "message_reference" in msg,
                        "referenced_message": msg.get('referenced_message'),
                    }

                    # if not is_content_valid(processed["content"]) or processed["timestamp"] < datetime.now().timestamp() - 86400:
                    if not is_content_valid(processed["content"], 100):
                        continue

                    # 标记是否提及当前帐号
                    processed["mentions_me"] = str(account["username"]) in processed["mentions"]

                    # 标记是否是回复我的消息
                    processed["is_reply_to_me"] = False
                    if processed["is_reply"] and processed["referenced_message"]:
                        processed["is_reply_to_me"] = (
                            processed["referenced_message"]["author"]["username"] == str(account["username"])
                        )
                    filtered_messages.append(processed)
                return list(reversed(filtered_messages))
        except Exception as e:
            StatusManager.add_log(account['name'], "ERROR", f"[{channel['name']}] 获取消息失败：{e}")
        return []