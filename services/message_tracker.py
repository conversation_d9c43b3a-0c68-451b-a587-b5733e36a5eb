import json
import os
import re
import threading
import time
from datetime import datetime
from typing import List, Dict

from services.status_manager import StatusManager


class MessageTracker:
    def __init__(self, account_name: str, channel_id: str):
        self.daily_sent = 0
        self.last_sent_date = datetime.now().date()
        self.account_name = account_name
        self.channel_id = channel_id
        self.replied_messages = dict()  # 已回复消息ID
        self.mentioned_history = {}     # 用户提及记录
        self.sent_history = dict()      # 已发送回复内容去重
        self.lock = threading.RLock()   # 细粒度锁
        self.last_reply_time = 0.0      # 最后回复时间（用于冷却）
        self.last_selected_time = 0.0   # 最后选中消息的时间戳
        self._load_from_disk()

    def _get_storage_path(self):
        """获取持久化文件路径"""
        os.makedirs(f"./message_tracker/{self.account_name}", exist_ok=True)
        return f"./message_tracker/{self.account_name}/{self.channel_id}.json"

    def _load_from_disk(self):
        """从磁盘加载数据"""
        print("从磁盘加载数据：", self.account_name, self.channel_id)
        path = self._get_storage_path()
        try:
            if os.path.exists(path):
                with open(path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 转换字符串key为数值类型（JSON默认用字符串保存数字key）
                    self.replied_messages = {k: float(v) for k, v in data.get("replied_messages", {}).items()}
                    self.mentioned_history = {k: float(v) for k, v in data.get("mentioned_history", {}).items()}
                    self.sent_history = {k: float(v) for k, v in data.get("sent_history", {}).items()}
                    self.last_reply_time = data.get("last_reply_time", 0.0)
                    self.last_selected_time = data.get("last_selected_time", 0.0)
                    self.daily_sent = data.get("daily_sent", 0)
                    saved_date = datetime.strptime(data["last_sent_date"], "%Y-%m-%d").date() if data.get("last_sent_date") else datetime.now().date()
                    # 日期变更时重置
                    if saved_date != datetime.now().date():
                        self.daily_sent = 0
                        self.last_sent_date = datetime.now().date()
                    else:
                        self.last_sent_date = saved_date
            pass
        except Exception as e:
            StatusManager.add_log(self.account_name, "ERROR", f"从磁盘加载数据失败: {e}")

    def _save_to_disk(self):
        """保存数据到磁盘"""
        data = {
            'replied_messages': self.replied_messages,
            'mentioned_history': self.mentioned_history,
            'sent_history': self.sent_history,
            'last_reply_time': self.last_reply_time,
            'last_selected_time': self.last_selected_time,
            'daily_sent': self.daily_sent,
            'last_sent_date': self.last_sent_date.strftime("%Y-%m-%d")
        }
        path = self._get_storage_path()
        try:
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                f.flush()  # 强制写入缓冲区
                os.fsync(f.fileno())  # 确保写入磁盘
        except Exception as e:
            StatusManager.add_log(self.account_name, "ERROR", f"保存数据到磁盘失败: {e}")

    def del_mentioned(self, message_id: str):
        with self.lock:
            del self.mentioned_history[message_id]
            self._save_to_disk()

    def add_replied(self, message_id: str):
        """记录已回复的消息"""
        with self.lock:
            self.replied_messages[message_id] = time.time()
            # 自动清理过期（保留3小时）
            expire = time.time() - 10800
            self.replied_messages = {k: v for k, v in self.replied_messages.items() if v > expire}
            self._save_to_disk()

    def is_replied(self, message_id: str) -> bool:
        with self.lock:
            return message_id in self.replied_messages

    def update_mentions(self, messages: List[Dict]):
        """更新提及记录并自动清理过期数据"""
        with self.lock:
            current_time = time.time()
            for msg in messages:
                if msg.get('mentions_me', False):
                    # 如果消息未被回复过则更新记录
                    if msg["id"] not in self.replied_messages:
                        self.mentioned_history[msg['id']] = current_time
            # 清理超过1个小时的记录（保持实时性）
            expire_time = current_time - 3600
            expired = [k for k, v in self.mentioned_history.items() if v < expire_time]
            for k in expired:
                del self.mentioned_history[k]
            self._save_to_disk()

    def get_active_mentions(self) -> List[str]:
        """获取当前有效的提及消息ID"""
        with self.lock:
            return list(self.mentioned_history.keys())

    def is_reply_duplicate(self, content: str) -> bool:
        with self.lock:
            # 清理1小时前的记录
            expire_time = time.time() - 3600
            self.sent_history = {k: v for k, v in self.sent_history.items() if v > expire_time}
            # 标准化内容后检查
            normalized = re.sub(r'\s+', ' ', content.strip()).lower()
            return any(normalized == re.sub(r'\s+','', k).lower() for k in self.sent_history.keys())

    def record_sent_reply(self, content: str):
        """记录已发送的回复内容"""
        with self.lock:
            self.sent_history[content] = time.time()
            self._save_to_disk()

    def increment_daily_count(self):
        if datetime.now().date() != self.last_sent_date:
            self.daily_sent = 0
            self.last_sent_date = datetime.now().date()
        self.daily_sent += 1
        self._save_to_disk()

    def get_daily_count(self):
        return self.daily_sent if datetime.now().date() == self.last_sent_date else 0