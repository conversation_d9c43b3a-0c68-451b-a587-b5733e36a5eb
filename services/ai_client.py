import threading
from typing import Dict

from openai import OpenAI


class AIClientFactory:
    def __init__(self):
        self.client: Dict[str, OpenAI] = {}
        self.lock = threading.RLock()

    def get_client(self, ai_config: Dict) -> OpenAI:
        code = ai_config.get('code', "deepseek")
        with self.lock:
            if code not in self.client:
                self.client[code] = OpenAI(api_key=ai_config['api_key'], base_url=ai_config['base_url'])
            return self.client[code]

ai_client = AIClientFactory()