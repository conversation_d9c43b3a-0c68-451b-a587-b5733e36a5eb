
import traceback
import sys
from typing import Optional, Any
import logging
import os
from datetime import datetime


class Logger:
    def __init__(self, log_name, console: bool = True):
        self.logger = logging.getLogger(log_name)
        self.logger.setLevel(logging.DEBUG)

        current_time = datetime.now().strftime('%Y%m%d')
        current_month = datetime.now().strftime('%Y%m')

        directory = f'logs/{current_month}'

        formatter = logging.Formatter(
            '[%(asctime)s] [%(levelname)s] - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 确保日志目录存在
        os.makedirs(directory, exist_ok=True)

        # 文件处理器 - 详细日志
        file_handler = logging.FileHandler(f'{directory}/{log_name}_{current_time}.log', encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.WARNING)
        self.logger.addHandler(file_handler)

        # 控制台处理器
        if console:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.DEBUG)
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)

    def debug(self, message: str):
        self.logger.debug(message)

    def info(self, message: str):
        self.logger.info(message)

    def warning(self, message: str):
        self.logger.warning(message)

    def error(self, message: str, exc_info=False):
        self.logger.error(message, exc_info=exc_info)

    def critical(self, message: str, exc_info=False):
        self.logger.critical(message, exc_info=exc_info)

logger = Logger("discord")