#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强功能的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.message_generator import (
    analyze_message_context, 
    get_personality_traits,
    detect_message_emotion,
    enhanced_post_processing,
    add_natural_elements,
    calculate_similarity,
    enhanced_reply_filter
)

def test_message_context_analysis():
    """测试消息上下文分析"""
    print("=== 测试消息上下文分析 ===")
    
    # 测试@提及
    mention_msg = {
        'content': '@runtu_small 你好吗？',
        'mentions_me': True,
        'is_reply_to_me': False,
        'is_reply': False
    }
    
    context = analyze_message_context(mention_msg, [], {'username': 'runtu_small'})
    print(f"@提及消息分析: {context['response_type']} - {context['situation_description']}")
    
    # 测试问题消息
    question_msg = {
        'content': '有人知道这个怎么用吗？',
        'mentions_me': False,
        'is_reply_to_me': False,
        'is_reply': False
    }
    
    context = analyze_message_context(question_msg, [], {'username': 'runtu_small'})
    print(f"问题消息分析: {context['response_type']} - {context['situation_description']}")
    
    # 测试积极情绪
    positive_msg = {
        'content': '哈哈太开心了！',
        'mentions_me': False,
        'is_reply_to_me': False,
        'is_reply': False
    }
    
    context = analyze_message_context(positive_msg, [], {'username': 'runtu_small'})
    print(f"积极情绪分析: {context['response_type']} - {context['situation_description']}")

def test_personality_traits():
    """测试性格特征生成"""
    print("\n=== 测试性格特征生成 ===")
    
    accounts = [
        {'age': 22, 'sex': '男', 'city': '深圳'},
        {'age': 28, 'sex': '女', 'city': '北京'},
        {'age': 35, 'sex': '男', 'city': '成都'}
    ]
    
    for account in accounts:
        traits = get_personality_traits(account)
        print(f"{account['age']}岁{account['sex']}性，{account['city']} -> {traits}")

def test_emotion_detection():
    """测试情感检测"""
    print("\n=== 测试情感检测 ===")
    
    test_messages = [
        "哈哈太好笑了😂",
        "真的很难过😢",
        "这个太让人生气了😡",
        "哇，太惊讶了！",
        "这个怎么用？不太懂",
        "今天天气不错"
    ]
    
    for msg in test_messages:
        emotion = detect_message_emotion(msg)
        print(f"'{msg}' -> {emotion if emotion else '无明显情感'}")

def test_post_processing():
    """测试后处理功能"""
    print("\n=== 测试后处理功能 ===")
    
    raw_replies = [
        "@runtu_small 你好！这是一个测试消息。",
        "**这是粗体文本**，还有`代码`",
        "这是一个非常长的消息，超过了正常的长度限制，需要进行智能截断处理，看看效果如何。这里还有更多内容，应该会被截断。",
        "＠用户　！？，。：；""''"
    ]
    
    account = {'username': 'runtu_small'}
    selected_message = {'content': '测试消息'}
    message_context = {'response_type': 'normal'}
    
    for raw in raw_replies:
        processed = enhanced_post_processing(raw, account, selected_message, message_context)
        print(f"原始: {raw}")
        print(f"处理后: {processed}")
        print()

def test_natural_elements():
    """测试自然化元素"""
    print("\n=== 测试自然化元素 ===")
    
    test_cases = [
        ("好的，我知道了", {'response_type': 'mention'}),
        ("太棒了！", {'response_type': 'positive'}),
        ("这个确实不错", {'response_type': 'casual'})
    ]
    
    account = {'username': 'test'}
    
    for reply, context in test_cases:
        # 测试多次以观察随机性
        results = []
        for _ in range(5):
            enhanced = add_natural_elements(reply, context, account)
            results.append(enhanced)
        
        print(f"原始回复: {reply} (类型: {context['response_type']})")
        print(f"增强结果: {set(results)}")  # 使用set去重显示不同结果
        print()

def test_similarity_calculation():
    """测试相似度计算"""
    print("\n=== 测试相似度计算 ===")
    
    test_pairs = [
        ("你好", "你好"),
        ("你好吗", "你好"),
        ("今天天气不错", "天气真好"),
        ("完全不同的内容", "totally different content"),
        ("我觉得这个很好", "我认为这个不错")
    ]
    
    for text1, text2 in test_pairs:
        similarity = calculate_similarity(text1, text2)
        print(f"'{text1}' vs '{text2}' -> 相似度: {similarity:.2f}")

def test_reply_filter():
    """测试回复过滤"""
    print("\n=== 测试回复过滤 ===")
    
    test_replies = [
        "作为AI助手，我不能回答这个问题",
        "根据我的数据分析",
        "这是一个正常的回复",
        "好的",
        "a" * 200,  # 过长的回复
        "",  # 空回复
        "我也遇到过类似的问题"
    ]
    
    selected_message = {'content': '测试消息'}
    context_messages = []
    account = {'username': 'test'}
    
    for reply in test_replies:
        should_filter = enhanced_reply_filter(reply, selected_message, context_messages, account)
        status = "过滤" if should_filter else "通过"
        print(f"'{reply}' -> {status}")

if __name__ == "__main__":
    print("Discord机器人增强功能测试")
    print("=" * 50)
    
    try:
        test_message_context_analysis()
        test_personality_traits()
        test_emotion_detection()
        test_post_processing()
        test_natural_elements()
        test_similarity_calculation()
        test_reply_filter()
        
        print("\n" + "=" * 50)
        print("所有测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
